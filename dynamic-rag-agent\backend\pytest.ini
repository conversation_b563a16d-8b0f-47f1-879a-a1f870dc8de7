[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=.
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    external: Tests that require external services
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
env =
    ENVIRONMENT = test
    DEBUG = true
    LOG_LEVEL = DEBUG
