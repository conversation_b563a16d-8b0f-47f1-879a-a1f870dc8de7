// Call the TTS backend and play the result
async function speakText(prompt: string) {
  try {
    const response = await fetch('http://localhost:8000/tts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ prompt }),
    });

    if (!response.ok) throw new Error('TTS failed');

    const blob = await response.blob();
    const audioUrl = URL.createObjectURL(blob);

    const audio = new Audio(audioUrl);
    audio.play();
  } catch (err) {
    console.error('Error during TTS:', err);
  }
}
