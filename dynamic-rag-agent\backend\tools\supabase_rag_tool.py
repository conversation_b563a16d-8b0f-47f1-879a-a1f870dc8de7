from google.adk.tools import Tool
from utils.embedding import get_embedding
from supabase import create_client
from config import get_settings
import logging

logger = logging.getLogger(__name__)
settings = get_settings()

# Initialize Supabase client
try:
    supabase = create_client(settings.supabase_url, settings.supabase_key)
    logger.info("Supabase client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Supabase client: {e}")
    raise

class SupabaseRAGTool(Tool):
    name = "supabase_rag"
    description = "Searches relevant information from Supabase documents using semantic similarity"

    def run(self, query: str) -> str:
        """
        Search for relevant documents using RAG

        Args:
            query: Search query

        Returns:
            Relevant document content or error message
        """
        try:
            logger.info(f"Searching for: {query}")

            # Get query embedding
            query_embedding = get_embedding(query)

            # Search for similar documents
            result = supabase.rpc("match_documents", {
                "query_embedding": query_embedding,
                "match_threshold": 0.75,
                "match_count": 5
            }).execute()

            if not result.data:
                logger.info("No relevant documents found")
                return "No relevant information found."

            matches = [r["content"] for r in result.data]
            logger.info(f"Found {len(matches)} relevant documents")
            return "\n---\n".join(matches)

        except Exception as e:
            logger.error(f"RAG search failed: {e}")
            return f"Search failed: {str(e)}"
