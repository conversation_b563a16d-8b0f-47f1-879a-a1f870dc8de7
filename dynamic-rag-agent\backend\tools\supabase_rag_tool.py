from google.adk.tools import Tool
from backend.utils.embedding import get_embedding
from supabase import create_client
import os

supabase = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_KEY"))

class SupabaseRAGTool(Tool):
    name = "supabase_rag"
    description = "Searches relevant information from Supabase documents"

    def run(self, query: str) -> str:
        query_embedding = get_embedding(query)  # <- Now from Ollama

        result = supabase.rpc("match_documents", {
            "query_embedding": query_embedding,
            "match_threshold": 0.75,
            "match_count": 5
        }).execute()

        if not result.data:
            return "No relevant information found."

        matches = [r["content"] for r in result.data]
        return "\n---\n".join(matches)
