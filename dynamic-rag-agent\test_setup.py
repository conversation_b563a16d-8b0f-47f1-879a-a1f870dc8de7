#!/usr/bin/env python3
"""
Setup validation script for the Dynamic RAG Agent
Tests all components and dependencies
"""
import sys
import os
import requests
import subprocess
import time
from pathlib import Path


def print_status(message, status="INFO"):
    """Print colored status message"""
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m",
        "WARNING": "\033[93m",
        "ERROR": "\033[91m",
        "RESET": "\033[0m"
    }
    print(f"{colors.get(status, '')}{status}: {message}{colors['RESET']}")


def check_python_version():
    """Check Python version"""
    print_status("Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print_status(f"Python {version.major}.{version.minor}.{version.micro} ✓", "SUCCESS")
        return True
    else:
        print_status(f"Python {version.major}.{version.minor}.{version.micro} - Need 3.11+", "ERROR")
        return False


def check_dependencies():
    """Check if required packages are installed"""
    print_status("Checking Python dependencies...")
    required_packages = [
        "fastapi", "uvicorn", "pydantic", "requests", 
        "supabase", "google-generativeai", "google-adk"
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print_status(f"  {package} ✓", "SUCCESS")
        except ImportError:
            print_status(f"  {package} ✗", "ERROR")
            missing.append(package)
    
    if missing:
        print_status(f"Missing packages: {', '.join(missing)}", "ERROR")
        print_status("Run: pip install -r requirements.txt", "INFO")
        return False
    
    return True


def check_environment_file():
    """Check if .env file exists and has required variables"""
    print_status("Checking environment configuration...")
    
    env_file = Path("backend/.env")
    if not env_file.exists():
        print_status(".env file not found", "ERROR")
        print_status("Copy backend/.env.example to backend/.env and configure", "INFO")
        return False
    
    required_vars = [
        "SUPABASE_URL", "SUPABASE_KEY", "GOOGLE_API_KEY"
    ]
    
    missing_vars = []
    with open(env_file) as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=" not in content or f"{var}=your_" in content:
                missing_vars.append(var)
    
    if missing_vars:
        print_status(f"Missing or unconfigured variables: {', '.join(missing_vars)}", "ERROR")
        return False
    
    print_status("Environment file configured ✓", "SUCCESS")
    return True


def check_ollama_service():
    """Check if Ollama service is running"""
    print_status("Checking Ollama service...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print_status("Ollama service running ✓", "SUCCESS")
            
            # Check if embedding model is available
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            if any("nomic-embed-text" in name for name in model_names):
                print_status("Embedding model available ✓", "SUCCESS")
                return True
            else:
                print_status("Embedding model not found", "WARNING")
                print_status("Run: ollama pull nomic-embed-text", "INFO")
                return False
        else:
            print_status(f"Ollama service error: {response.status_code}", "ERROR")
            return False
    except requests.exceptions.RequestException:
        print_status("Ollama service not running", "ERROR")
        print_status("Start Ollama: ollama serve", "INFO")
        return False


def check_supabase_connection():
    """Check Supabase connection"""
    print_status("Checking Supabase connection...")
    
    # This would require loading the actual environment
    # For now, just check if the variables are set
    env_file = Path("backend/.env")
    if env_file.exists():
        with open(env_file) as f:
            content = f.read()
            if "SUPABASE_URL=https://" in content and "SUPABASE_KEY=" in content:
                print_status("Supabase configuration found ✓", "SUCCESS")
                return True
    
    print_status("Supabase configuration incomplete", "WARNING")
    return False


def test_api_startup():
    """Test if the API can start"""
    print_status("Testing API startup...")
    
    # Change to backend directory
    os.chdir("backend")
    
    try:
        # Start the server in background
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a bit for startup
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is None:
            # Try to connect
            try:
                response = requests.get("http://localhost:8000/health", timeout=10)
                if response.status_code == 200:
                    print_status("API startup successful ✓", "SUCCESS")
                    process.terminate()
                    process.wait()
                    return True
                else:
                    print_status(f"API health check failed: {response.status_code}", "ERROR")
            except requests.exceptions.RequestException as e:
                print_status(f"API connection failed: {e}", "ERROR")
        else:
            # Process died
            stdout, stderr = process.communicate()
            print_status("API startup failed", "ERROR")
            if stderr:
                print_status(f"Error: {stderr.decode()}", "ERROR")
        
        # Clean up
        if process.poll() is None:
            process.terminate()
            process.wait()
        
        return False
        
    except Exception as e:
        print_status(f"Error testing API: {e}", "ERROR")
        return False
    finally:
        os.chdir("..")


def run_tests():
    """Run the test suite"""
    print_status("Running test suite...")
    
    os.chdir("backend")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pytest", "tests/", "-v", "--tb=short"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print_status("All tests passed ✓", "SUCCESS")
            return True
        else:
            print_status("Some tests failed", "WARNING")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print_status(f"Error running tests: {e}", "ERROR")
        return False
    finally:
        os.chdir("..")


def main():
    """Main validation function"""
    print_status("=== Dynamic RAG Agent Setup Validation ===", "INFO")
    print()
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Environment", check_environment_file),
        ("Ollama Service", check_ollama_service),
        ("Supabase Config", check_supabase_connection),
    ]
    
    results = {}
    for name, check_func in checks:
        results[name] = check_func()
        print()
    
    # Summary
    print_status("=== Validation Summary ===", "INFO")
    passed = sum(results.values())
    total = len(results)
    
    for name, result in results.items():
        status = "✓" if result else "✗"
        color = "SUCCESS" if result else "ERROR"
        print_status(f"{name}: {status}", color)
    
    print()
    print_status(f"Passed: {passed}/{total}", "INFO")
    
    if passed == total:
        print_status("All checks passed! System ready for deployment.", "SUCCESS")
        
        # Optional: Run API test
        if input("\nRun API startup test? (y/N): ").lower() == 'y':
            if test_api_startup():
                print_status("API test passed ✓", "SUCCESS")
            else:
                print_status("API test failed ✗", "ERROR")
        
        # Optional: Run test suite
        if input("\nRun test suite? (y/N): ").lower() == 'y':
            run_tests()
        
        return 0
    else:
        print_status("Some checks failed. Please fix the issues above.", "ERROR")
        return 1


if __name__ == "__main__":
    sys.exit(main())
