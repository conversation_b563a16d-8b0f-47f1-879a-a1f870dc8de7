"""
Tests for the main FastAPI application
"""
import pytest
from unittest.mock import patch, Mock
from fastapi.testclient import TestClient


def test_health_endpoint(client):
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] in ["healthy", "degraded", "unhealthy"]
    assert "timestamp" in data
    assert "uptime" in data


def test_chat_endpoint_success(client):
    """Test successful chat request"""
    response = client.post("/chat", json={"message": "Hello, world!"})
    assert response.status_code == 200
    data = response.json()
    assert "response" in data
    assert data["response"] == "Test agent response"
    assert data["audio_url"] is None


def test_chat_endpoint_validation_error(client):
    """Test chat endpoint with invalid input"""
    response = client.post("/chat", json={})
    assert response.status_code == 422


def test_chat_endpoint_empty_message(client):
    """Test chat endpoint with empty message"""
    response = client.post("/chat", json={"message": ""})
    assert response.status_code == 200  # Empty message should still work


@patch('main.requests.post')
def test_agent_audio_endpoint_success(mock_requests, client):
    """Test successful agent-audio request"""
    # Mock TTS response
    mock_response = Mock()
    mock_response.content = b"fake audio data"
    mock_response.raise_for_status.return_value = None
    mock_requests.return_value = mock_response
    
    response = client.post("/agent-audio", json={"message": "Hello, world!"})
    assert response.status_code == 200
    data = response.json()
    assert "response" in data
    assert "audio_url" in data
    assert data["response"] == "Test agent response"
    assert data["audio_url"] is not None


@patch('main.requests.post')
def test_agent_audio_endpoint_tts_failure(mock_requests, client):
    """Test agent-audio endpoint when TTS fails"""
    # Mock TTS failure
    mock_requests.side_effect = Exception("TTS service unavailable")
    
    response = client.post("/agent-audio", json={"message": "Hello, world!"})
    assert response.status_code == 200
    data = response.json()
    assert "response" in data
    assert data["response"] == "Test agent response"
    assert data["audio_url"] is None  # Should be None when TTS fails


def test_serve_audio_file_not_found(client):
    """Test serving non-existent audio file"""
    response = client.get("/audio/nonexistent.wav")
    assert response.status_code == 404


def test_cors_headers(client):
    """Test CORS headers are present"""
    response = client.options("/chat")
    assert "access-control-allow-origin" in response.headers


def test_security_headers(client):
    """Test security headers are present"""
    response = client.get("/health")
    assert response.headers.get("X-Content-Type-Options") == "nosniff"
    assert response.headers.get("X-Frame-Options") == "DENY"
    assert response.headers.get("X-XSS-Protection") == "1; mode=block"


def test_request_logging(client, caplog):
    """Test request logging middleware"""
    response = client.get("/health")
    assert response.status_code == 200
    # Check that request was logged
    assert any("GET /health" in record.message for record in caplog.records)


@patch('main.run_agent')
def test_agent_error_handling(mock_run_agent, client):
    """Test agent error handling"""
    mock_run_agent.side_effect = Exception("Agent processing failed")
    
    response = client.post("/chat", json={"message": "test"})
    assert response.status_code == 500
