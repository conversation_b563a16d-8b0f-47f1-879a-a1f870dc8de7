"""
Pytest configuration and fixtures for the Dynamic RAG Agent tests
"""
import pytest
import os
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from config import Settings


@pytest.fixture
def test_settings():
    """Test settings fixture"""
    return Settings(
        supabase_url="http://test-supabase.com",
        supabase_key="test-key",
        google_api_key="test-google-key",
        ollama_url="http://test-ollama:11434",
        embedding_model="test-model",
        chatterbox_url="http://test-chatterbox:8004",
        tts_voice="test-voice",
        cors_origins=["http://localhost:3000"],
        log_level="DEBUG",
        environment="test",
        debug=True,
        audio_dir="./test_audio"
    )


@pytest.fixture
def mock_supabase():
    """Mock Supabase client"""
    with patch('tools.supabase_rag_tool.supabase') as mock:
        mock.rpc.return_value.execute.return_value.data = [
            {"content": "Test document content 1"},
            {"content": "Test document content 2"}
        ]
        yield mock


@pytest.fixture
def mock_ollama():
    """Mock Ollama embedding service"""
    with patch('utils.embedding.requests.post') as mock:
        mock.return_value.json.return_value = {"embedding": [0.1] * 768}
        mock.return_value.raise_for_status.return_value = None
        yield mock


@pytest.fixture
def mock_agent():
    """Mock agent runner"""
    with patch('agent.runner') as mock:
        mock.run.return_value.text = "Test agent response"
        yield mock


@pytest.fixture
def client(test_settings, mock_supabase, mock_ollama, mock_agent):
    """Test client fixture"""
    with patch('config.get_settings', return_value=test_settings):
        from main import app
        with TestClient(app) as client:
            yield client


@pytest.fixture
def temp_audio_dir(tmp_path):
    """Temporary audio directory for tests"""
    audio_dir = tmp_path / "audio"
    audio_dir.mkdir()
    return str(audio_dir)


@pytest.fixture(autouse=True)
def setup_test_env(monkeypatch, temp_audio_dir):
    """Setup test environment variables"""
    monkeypatch.setenv("SUPABASE_URL", "http://test-supabase.com")
    monkeypatch.setenv("SUPABASE_KEY", "test-key")
    monkeypatch.setenv("GOOGLE_API_KEY", "test-google-key")
    monkeypatch.setenv("ENVIRONMENT", "test")
    monkeypatch.setenv("AUDIO_DIR", temp_audio_dir)
