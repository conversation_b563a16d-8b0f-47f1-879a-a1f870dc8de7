# API Documentation

## Overview

The Dynamic RAG Agent API provides endpoints for AI-powered chat with RAG capabilities and optional text-to-speech functionality.

**Base URL**: `http://localhost:8000`

## Authentication

Currently, the API does not require authentication. In production, implement proper authentication mechanisms.

## Rate Limiting

- **Default**: 60 requests per minute per IP
- **Headers**: Rate limit information is included in response headers

## Error Handling

All errors follow a consistent format:

```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "request_id": "uuid-request-id",
  "details": {} // Optional additional details
}
```

### Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Invalid request data | 422 |
| `AGENT_ERROR` | Agent processing failed | 500 |
| `RAG_ERROR` | RAG search failed | 500 |
| `EMBEDDING_ERROR` | Embedding generation failed | 500 |
| `TTS_ERROR` | Text-to-speech failed | 500 |
| `HTTP_404` | Resource not found | 404 |
| `HTTP_429` | Rate limit exceeded | 429 |
| `INTERNAL_ERROR` | Unexpected server error | 500 |

## Endpoints

### Health Check

#### GET /health

Basic health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********.789,
  "version": "1.0.0",
  "uptime": 3600.5,
  "system": {
    "cpu_percent": 25.5,
    "memory_percent": 60.0,
    "disk_percent": 45.0
  },
  "services": {
    "supabase": "healthy",
    "ollama": "healthy"
  }
}
```

**Status Values:**
- `healthy`: All systems operational
- `degraded`: Some non-critical services unavailable
- `unhealthy`: Critical services unavailable

#### GET /health/detailed

Detailed health check with dependency information.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********.789,
  "version": "1.0.0",
  "uptime": 3600.5,
  "system": {
    "cpu_percent": 25.5,
    "memory_percent": 60.0,
    "disk_percent": 45.0
  },
  "services": {
    "supabase": "healthy",
    "ollama": "healthy"
  },
  "dependencies": {
    "supabase": {
      "status": "healthy",
      "response_time": 0.1,
      "details": "Connection successful"
    },
    "ollama": {
      "status": "healthy",
      "response_time": 0.2,
      "details": "Service available"
    },
    "chatterbox": {
      "status": "degraded",
      "error": "Connection refused",
      "details": "TTS service unavailable (optional)"
    }
  }
}
```

#### GET /metrics

Application metrics endpoint.

**Response:**
```json
{
  "uptime_seconds": 3600.5,
  "timestamp": **********.789,
  "system": {
    "cpu_percent": 25.5,
    "memory_percent": 60.0,
    "disk_percent": 45.0
  },
  "requests_total": 1234,
  "errors_total": 5
}
```

### Chat

#### POST /chat

Basic chat endpoint without text-to-speech.

**Request:**
```json
{
  "message": "What is machine learning?"
}
```

**Response:**
```json
{
  "response": "Machine learning is a subset of artificial intelligence that focuses on the development of algorithms and statistical models that enable computer systems to improve their performance on a specific task through experience, without being explicitly programmed for that task.",
  "audio_url": null
}
```

**Curl Example:**
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "What is machine learning?"}'
```

#### POST /agent-audio

Chat endpoint with optional text-to-speech audio generation.

**Request:**
```json
{
  "message": "Explain quantum computing"
}
```

**Response:**
```json
{
  "response": "Quantum computing is a type of computation that harnesses the collective properties of quantum states, such as superposition, interference, and entanglement, to process information in ways that classical computers cannot.",
  "audio_url": "/audio/12345678-1234-1234-1234-123456789abc.wav"
}
```

**Notes:**
- If TTS service is unavailable, `audio_url` will be `null`
- Audio files are automatically cleaned up periodically
- Audio format is WAV

**Curl Example:**
```bash
curl -X POST "http://localhost:8000/agent-audio" \
  -H "Content-Type: application/json" \
  -d '{"message": "Explain quantum computing"}'
```

### Audio

#### GET /audio/{filename}

Serve generated audio files.

**Parameters:**
- `filename`: Audio file name (UUID.wav format)

**Response:**
- **Success**: Audio file (WAV format)
- **Not Found**: 404 error if file doesn't exist

**Curl Example:**
```bash
curl -O "http://localhost:8000/audio/12345678-1234-1234-1234-123456789abc.wav"
```

## Request/Response Headers

### Common Request Headers

| Header | Description | Required |
|--------|-------------|----------|
| `Content-Type` | Must be `application/json` for POST requests | Yes |
| `Accept` | Response format preference | No |

### Common Response Headers

| Header | Description |
|--------|-------------|
| `Content-Type` | Response content type |
| `X-Request-ID` | Unique request identifier |
| `X-Process-Time` | Request processing time in seconds |
| `X-Content-Type-Options` | Security header (nosniff) |
| `X-Frame-Options` | Security header (DENY) |
| `X-XSS-Protection` | Security header |

## WebSocket Support

Currently not implemented. All communication is via HTTP REST API.

## SDK Examples

### Python

```python
import requests

# Basic chat
response = requests.post(
    "http://localhost:8000/chat",
    json={"message": "Hello, world!"}
)
data = response.json()
print(data["response"])

# Chat with audio
response = requests.post(
    "http://localhost:8000/agent-audio",
    json={"message": "Tell me a joke"}
)
data = response.json()
print(data["response"])

if data["audio_url"]:
    # Download audio
    audio_response = requests.get(f"http://localhost:8000{data['audio_url']}")
    with open("response.wav", "wb") as f:
        f.write(audio_response.content)
```

### JavaScript

```javascript
// Basic chat
const response = await fetch('http://localhost:8000/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    message: 'Hello, world!'
  })
});

const data = await response.json();
console.log(data.response);

// Chat with audio
const audioResponse = await fetch('http://localhost:8000/agent-audio', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    message: 'Tell me a joke'
  })
});

const audioData = await audioResponse.json();
console.log(audioData.response);

if (audioData.audio_url) {
  // Play audio
  const audio = new Audio(`http://localhost:8000${audioData.audio_url}`);
  audio.play();
}
```

## OpenAPI/Swagger

Interactive API documentation is available at:
- **Swagger UI**: `http://localhost:8000/docs` (development only)
- **ReDoc**: `http://localhost:8000/redoc` (development only)

## Changelog

### v1.0.0
- Initial release
- Basic chat functionality
- RAG search integration
- TTS support
- Health monitoring
- Comprehensive error handling
