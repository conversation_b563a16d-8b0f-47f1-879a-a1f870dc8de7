"""
Health check endpoints and monitoring for the Dynamic RAG Agent
"""
import time
import psutil
from typing import Dict, Any
from fastapi import APIRouter, Depends
from pydantic import BaseModel
from config import get_settings
from supabase import create_client
import requests
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


class HealthStatus(BaseModel):
    status: str
    timestamp: float
    version: str = "1.0.0"
    uptime: float
    system: Dict[str, Any]
    services: Dict[str, str]


class DetailedHealthStatus(HealthStatus):
    dependencies: Dict[str, Dict[str, Any]]


# Track startup time
startup_time = time.time()


def get_system_info() -> Dict[str, Any]:
    """Get system information"""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent,
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    except Exception as e:
        logger.warning(f"Could not get system info: {e}")
        return {"error": str(e)}


def check_supabase_health(settings) -> Dict[str, Any]:
    """Check Supabase connection"""
    try:
        client = create_client(settings.supabase_url, settings.supabase_key)
        # Simple query to test connection
        result = client.table("health_check").select("*").limit(1).execute()
        return {
            "status": "healthy",
            "response_time": 0.1,  # Would measure actual time
            "details": "Connection successful"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "details": "Connection failed"
        }


def check_ollama_health(settings) -> Dict[str, Any]:
    """Check Ollama service"""
    try:
        response = requests.get(f"{settings.ollama_url}/api/tags", timeout=5)
        response.raise_for_status()
        return {
            "status": "healthy",
            "response_time": response.elapsed.total_seconds(),
            "details": "Service available"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "details": "Service unavailable"
        }


def check_chatterbox_health(settings) -> Dict[str, Any]:
    """Check Chatterbox TTS service"""
    try:
        response = requests.get(f"{settings.chatterbox_url}/health", timeout=5)
        response.raise_for_status()
        return {
            "status": "healthy",
            "response_time": response.elapsed.total_seconds(),
            "details": "Service available"
        }
    except Exception as e:
        return {
            "status": "degraded",  # TTS is optional
            "error": str(e),
            "details": "TTS service unavailable (optional)"
        }


@router.get("/health", response_model=HealthStatus)
def health_check(settings = Depends(get_settings)):
    """Basic health check"""
    current_time = time.time()
    uptime = current_time - startup_time
    
    # Check critical services
    services = {}
    
    # Check Supabase (critical)
    supabase_health = check_supabase_health(settings)
    services["supabase"] = supabase_health["status"]
    
    # Check Ollama (critical for embeddings)
    ollama_health = check_ollama_health(settings)
    services["ollama"] = ollama_health["status"]
    
    # Determine overall status
    critical_services = ["supabase", "ollama"]
    overall_status = "healthy"
    
    for service in critical_services:
        if services.get(service) == "unhealthy":
            overall_status = "unhealthy"
            break
        elif services.get(service) == "degraded":
            overall_status = "degraded"
    
    return HealthStatus(
        status=overall_status,
        timestamp=current_time,
        uptime=uptime,
        system=get_system_info(),
        services=services
    )


@router.get("/health/detailed", response_model=DetailedHealthStatus)
def detailed_health_check(settings = Depends(get_settings)):
    """Detailed health check with dependency information"""
    basic_health = health_check(settings)
    
    # Get detailed dependency information
    dependencies = {
        "supabase": check_supabase_health(settings),
        "ollama": check_ollama_health(settings),
        "chatterbox": check_chatterbox_health(settings)
    }
    
    return DetailedHealthStatus(
        **basic_health.dict(),
        dependencies=dependencies
    )


@router.get("/metrics")
def get_metrics():
    """Get application metrics"""
    current_time = time.time()
    uptime = current_time - startup_time
    
    return {
        "uptime_seconds": uptime,
        "timestamp": current_time,
        "system": get_system_info(),
        # Add more metrics as needed
        "requests_total": 0,  # Would track actual requests
        "errors_total": 0,    # Would track actual errors
    }
