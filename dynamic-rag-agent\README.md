# Dynamic RAG Agent

A production-ready AI agent with Retrieval-Augmented Generation (RAG) capabilities, built with FastAPI and Google's Agent Development Kit (ADK).

## Features

- 🤖 **AI Agent**: Powered by Google's Agent Development Kit with multiple tools
- 🔍 **RAG Search**: Semantic search using Supabase vector database
- 🎤 **Text-to-Speech**: Optional TTS integration with Chatterbox
- 🚀 **Production Ready**: Comprehensive logging, monitoring, and error handling
- 🐳 **Containerized**: Docker and Docker Compose support
- 🧪 **Well Tested**: Comprehensive test suite with high coverage
- 📊 **Health Monitoring**: Built-in health checks and metrics
- 🔒 **Secure**: Security headers, rate limiting, and input validation

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Supabase DB   │
│   (React/Vue)   │◄──►│   (FastAPI)     │◄──►│   (Vector DB)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Ollama        │
                       │   (Embeddings)  │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Chatterbox    │
                       │   (TTS)         │
                       └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.11+
- Dock<PERSON> and <PERSON>er Compose (optional)
- Supabase account
- Google AI API key
- Ollama (for embeddings)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dynamic-rag-agent
   ```

2. **Set up the backend**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

4. **Run the application**
   ```bash
   python main.py
   ```

### Using Docker

1. **Configure environment**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env with your actual values
   ```

2. **Start services**
   ```bash
   docker-compose up -d
   ```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `SUPABASE_URL` | Supabase project URL | - | Yes |
| `SUPABASE_KEY` | Supabase anon key | - | Yes |
| `GOOGLE_API_KEY` | Google AI API key | - | Yes |
| `OLLAMA_URL` | Ollama service URL | `http://localhost:11434` | No |
| `EMBEDDING_MODEL` | Embedding model name | `nomic-embed-text` | No |
| `CHATTERBOX_URL` | TTS service URL | `http://localhost:8004` | No |
| `CORS_ORIGINS` | Allowed CORS origins | `http://localhost:3000,http://localhost:5173` | No |
| `LOG_LEVEL` | Logging level | `INFO` | No |
| `ENVIRONMENT` | Environment name | `development` | No |
| `DEBUG` | Debug mode | `true` | No |

### Supabase Setup

1. Create a new Supabase project
2. Create a table for documents with vector embeddings
3. Set up the `match_documents` RPC function for similarity search

Example SQL:
```sql
-- Create documents table
CREATE TABLE documents (
  id SERIAL PRIMARY KEY,
  content TEXT NOT NULL,
  embedding VECTOR(768),
  metadata JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create similarity search function
CREATE OR REPLACE FUNCTION match_documents(
  query_embedding VECTOR(768),
  match_threshold FLOAT DEFAULT 0.75,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id INT,
  content TEXT,
  similarity FLOAT
)
LANGUAGE SQL STABLE
AS $$
  SELECT
    documents.id,
    documents.content,
    1 - (documents.embedding <=> query_embedding) AS similarity
  FROM documents
  WHERE 1 - (documents.embedding <=> query_embedding) > match_threshold
  ORDER BY similarity DESC
  LIMIT match_count;
$$;
```

## API Documentation

### Endpoints

#### Health Check
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health with dependencies
- `GET /metrics` - Application metrics

#### Chat
- `POST /chat` - Basic chat without TTS
- `POST /agent-audio` - Chat with optional TTS audio

#### Audio
- `GET /audio/{filename}` - Serve audio files

### Request/Response Examples

**Chat Request:**
```json
{
  "message": "What is machine learning?"
}
```

**Chat Response:**
```json
{
  "response": "Machine learning is a subset of artificial intelligence...",
  "audio_url": null
}
```

**Agent Audio Response:**
```json
{
  "response": "Machine learning is a subset of artificial intelligence...",
  "audio_url": "/audio/12345678-1234-1234-1234-123456789abc.wav"
}
```

## Development

### Running Tests

```bash
# Install test dependencies
pip install -r requirements.txt

# Run all tests
python run_tests.py

# Run specific test types
python run_tests.py unit
python run_tests.py integration
python run_tests.py --verbose

# Run with coverage
python run_tests.py --coverage
```

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .

# Type checking
mypy .
```

### Project Structure

```
dynamic-rag-agent/
├── backend/
│   ├── agent.py              # Agent logic
│   ├── main.py               # FastAPI application
│   ├── config.py             # Configuration
│   ├── logging_config.py     # Logging setup
│   ├── middleware.py         # Custom middleware
│   ├── exceptions.py         # Custom exceptions
│   ├── health.py             # Health checks
│   ├── tools/                # Agent tools
│   │   ├── __init__.py
│   │   └── supabase_rag_tool.py
│   ├── utils/                # Utilities
│   │   ├── __init__.py
│   │   └── embedding.py
│   ├── tests/                # Test suite
│   ├── requirements.txt      # Dependencies
│   ├── Dockerfile           # Container definition
│   └── .env.example         # Environment template
├── frontend/                 # Frontend application
├── docker-compose.yml       # Multi-service setup
└── README.md                # This file
```

## Deployment

### Docker Deployment

1. **Build and run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

2. **Scale services:**
   ```bash
   docker-compose up -d --scale backend=3
   ```

### Production Deployment

1. **Set production environment:**
   ```bash
   export ENVIRONMENT=production
   export DEBUG=false
   export LOG_LEVEL=WARNING
   ```

2. **Use a production WSGI server:**
   ```bash
   gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

3. **Set up reverse proxy (Nginx):**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### Monitoring

- Health checks: `GET /health`
- Metrics: `GET /metrics`
- Logs: Check `logs/` directory
- Application monitoring: Use tools like Prometheus + Grafana

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure all dependencies are installed
2. **Supabase connection**: Check URL and API key
3. **Ollama not available**: Ensure Ollama is running and accessible
4. **TTS failures**: TTS is optional, agent will work without it

### Debug Mode

Enable debug mode for detailed logging:
```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Performance Optimization

### Production Optimizations

1. **Database Connection Pooling**: Configure Supabase connection pooling
2. **Caching**: Implement Redis for caching embeddings and responses
3. **Load Balancing**: Use multiple backend instances behind a load balancer
4. **CDN**: Use a CDN for serving audio files
5. **Monitoring**: Set up application performance monitoring (APM)

### Scaling Considerations

- **Horizontal Scaling**: Run multiple backend instances
- **Database Scaling**: Use read replicas for Supabase
- **Embedding Caching**: Cache frequently used embeddings
- **Async Processing**: Use background tasks for heavy operations

## Security

### Security Features

- **Input Validation**: All inputs are validated using Pydantic
- **Rate Limiting**: Built-in rate limiting middleware
- **Security Headers**: OWASP recommended security headers
- **CORS Protection**: Configurable CORS origins
- **Error Handling**: Secure error messages without sensitive data

### Security Best Practices

1. **Environment Variables**: Never commit secrets to version control
2. **HTTPS**: Always use HTTPS in production
3. **API Keys**: Rotate API keys regularly
4. **Access Control**: Implement proper authentication/authorization
5. **Monitoring**: Monitor for suspicious activity

## Support

For support, please open an issue on GitHub or contact the development team.
