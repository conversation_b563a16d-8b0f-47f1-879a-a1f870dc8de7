# Deployment Guide

This guide covers various deployment options for the Dynamic RAG Agent.

## Table of Contents

1. [Local Development](#local-development)
2. [Docker Deployment](#docker-deployment)
3. [Production Deployment](#production-deployment)
4. [Cloud Deployment](#cloud-deployment)
5. [Monitoring and Maintenance](#monitoring-and-maintenance)

## Local Development

### Prerequisites

- Python 3.11+
- Node.js 18+ (for frontend)
- Supabase account
- Google AI API key
- Ollama (for embeddings)

### Setup

1. **Clone and setup backend:**
   ```bash
   git clone <repository-url>
   cd dynamic-rag-agent/backend
   python -m venv venv
   source venv/bin/activate  # Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your values
   ```

3. **Setup Supabase:**
   - Create a new project
   - Run the SQL setup from README.md
   - Get your URL and anon key

4. **Start Ollama:**
   ```bash
   ollama serve
   ollama pull nomic-embed-text
   ```

5. **Run the application:**
   ```bash
   python main.py
   ```

## Docker Deployment

### Single Container

1. **Build the image:**
   ```bash
   cd backend
   docker build -t dynamic-rag-agent .
   ```

2. **Run the container:**
   ```bash
   docker run -d \
     --name rag-agent \
     -p 8000:8000 \
     --env-file .env \
     dynamic-rag-agent
   ```

### Docker Compose (Recommended)

1. **Configure environment:**
   ```bash
   cp backend/.env.example backend/.env
   # Edit backend/.env
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f backend
   ```

4. **Scale backend:**
   ```bash
   docker-compose up -d --scale backend=3
   ```

### Docker Compose Profiles

Use profiles to enable optional services:

```bash
# With TTS service
docker-compose --profile tts up -d

# With caching
docker-compose --profile cache up -d

# With reverse proxy
docker-compose --profile proxy up -d

# All services
docker-compose --profile tts --profile cache --profile proxy up -d
```

## Production Deployment

### Environment Configuration

1. **Create production environment file:**
   ```bash
   cp backend/.env.example backend/.env.prod
   ```

2. **Set production values:**
   ```env
   ENVIRONMENT=production
   DEBUG=false
   LOG_LEVEL=WARNING
   HOST=0.0.0.0
   PORT=8000
   WORKERS=4
   
   # Use production URLs
   SUPABASE_URL=https://your-project.supabase.co
   OLLAMA_URL=http://ollama-service:11434
   CHATTERBOX_URL=http://tts-service:8004
   
   # Restrict CORS
   CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
   ```

### Using Gunicorn

1. **Install Gunicorn:**
   ```bash
   pip install gunicorn
   ```

2. **Create Gunicorn config:**
   ```python
   # gunicorn.conf.py
   bind = "0.0.0.0:8000"
   workers = 4
   worker_class = "uvicorn.workers.UvicornWorker"
   worker_connections = 1000
   max_requests = 1000
   max_requests_jitter = 100
   timeout = 30
   keepalive = 2
   preload_app = True
   ```

3. **Run with Gunicorn:**
   ```bash
   gunicorn main:app -c gunicorn.conf.py
   ```

### Systemd Service

1. **Create service file:**
   ```ini
   # /etc/systemd/system/rag-agent.service
   [Unit]
   Description=Dynamic RAG Agent
   After=network.target
   
   [Service]
   Type=exec
   User=raguser
   Group=raguser
   WorkingDirectory=/opt/rag-agent
   Environment=PATH=/opt/rag-agent/venv/bin
   EnvironmentFile=/opt/rag-agent/.env
   ExecStart=/opt/rag-agent/venv/bin/gunicorn main:app -c gunicorn.conf.py
   ExecReload=/bin/kill -s HUP $MAINPID
   Restart=always
   RestartSec=5
   
   [Install]
   WantedBy=multi-user.target
   ```

2. **Enable and start:**
   ```bash
   sudo systemctl enable rag-agent
   sudo systemctl start rag-agent
   sudo systemctl status rag-agent
   ```

### Nginx Configuration

1. **Install Nginx:**
   ```bash
   sudo apt update
   sudo apt install nginx
   ```

2. **Create site configuration:**
   ```nginx
   # /etc/nginx/sites-available/rag-agent
   server {
       listen 80;
       server_name yourdomain.com www.yourdomain.com;
       
       # Redirect HTTP to HTTPS
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl http2;
       server_name yourdomain.com www.yourdomain.com;
       
       # SSL Configuration
       ssl_certificate /path/to/your/certificate.crt;
       ssl_certificate_key /path/to/your/private.key;
       ssl_protocols TLSv1.2 TLSv1.3;
       ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
       ssl_prefer_server_ciphers off;
       
       # Security Headers
       add_header X-Frame-Options DENY;
       add_header X-Content-Type-Options nosniff;
       add_header X-XSS-Protection "1; mode=block";
       add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
       
       # Rate Limiting
       limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
       
       # API Proxy
       location /api/ {
           limit_req zone=api burst=20 nodelay;
           
           proxy_pass http://127.0.0.1:8000/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           
           # Timeouts
           proxy_connect_timeout 60s;
           proxy_send_timeout 60s;
           proxy_read_timeout 60s;
       }
       
       # Static Files
       location /static/ {
           alias /opt/rag-agent/static/;
           expires 1d;
           add_header Cache-Control "public, immutable";
       }
       
       # Frontend (if serving from same domain)
       location / {
           root /opt/rag-agent/frontend/dist;
           try_files $uri $uri/ /index.html;
       }
   }
   ```

3. **Enable site:**
   ```bash
   sudo ln -s /etc/nginx/sites-available/rag-agent /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## Cloud Deployment

### AWS Deployment

#### Using ECS (Elastic Container Service)

1. **Build and push to ECR:**
   ```bash
   # Create ECR repository
   aws ecr create-repository --repository-name dynamic-rag-agent
   
   # Get login token
   aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-west-2.amazonaws.com
   
   # Build and tag
   docker build -t dynamic-rag-agent .
   docker tag dynamic-rag-agent:latest <account-id>.dkr.ecr.us-west-2.amazonaws.com/dynamic-rag-agent:latest
   
   # Push
   docker push <account-id>.dkr.ecr.us-west-2.amazonaws.com/dynamic-rag-agent:latest
   ```

2. **Create ECS task definition:**
   ```json
   {
     "family": "dynamic-rag-agent",
     "networkMode": "awsvpc",
     "requiresCompatibilities": ["FARGATE"],
     "cpu": "512",
     "memory": "1024",
     "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
     "containerDefinitions": [
       {
         "name": "rag-agent",
         "image": "<account-id>.dkr.ecr.us-west-2.amazonaws.com/dynamic-rag-agent:latest",
         "portMappings": [
           {
             "containerPort": 8000,
             "protocol": "tcp"
           }
         ],
         "environment": [
           {
             "name": "ENVIRONMENT",
             "value": "production"
           }
         ],
         "secrets": [
           {
             "name": "SUPABASE_URL",
             "valueFrom": "arn:aws:secretsmanager:us-west-2:<account-id>:secret:rag-agent/supabase-url"
           }
         ],
         "logConfiguration": {
           "logDriver": "awslogs",
           "options": {
             "awslogs-group": "/ecs/dynamic-rag-agent",
             "awslogs-region": "us-west-2",
             "awslogs-stream-prefix": "ecs"
           }
         }
       }
     ]
   }
   ```

#### Using Lambda (Serverless)

1. **Install Mangum:**
   ```bash
   pip install mangum
   ```

2. **Create Lambda handler:**
   ```python
   # lambda_handler.py
   from mangum import Mangum
   from main import app
   
   handler = Mangum(app)
   ```

3. **Deploy with AWS SAM:**
   ```yaml
   # template.yaml
   AWSTemplateFormatVersion: '2010-09-09'
   Transform: AWS::Serverless-2016-10-31
   
   Resources:
     RagAgentFunction:
       Type: AWS::Serverless::Function
       Properties:
         CodeUri: .
         Handler: lambda_handler.handler
         Runtime: python3.11
         Timeout: 30
         MemorySize: 512
         Environment:
           Variables:
             ENVIRONMENT: production
         Events:
           Api:
             Type: Api
             Properties:
               Path: /{proxy+}
               Method: ANY
   ```

### Google Cloud Platform

#### Using Cloud Run

1. **Create Dockerfile for Cloud Run:**
   ```dockerfile
   FROM python:3.11-slim
   
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   
   COPY . .
   
   CMD exec gunicorn --bind :$PORT --workers 1 --worker-class uvicorn.workers.UvicornWorker main:app
   ```

2. **Deploy to Cloud Run:**
   ```bash
   gcloud run deploy dynamic-rag-agent \
     --source . \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --set-env-vars ENVIRONMENT=production
   ```

### Azure Deployment

#### Using Container Instances

1. **Create resource group:**
   ```bash
   az group create --name rag-agent-rg --location eastus
   ```

2. **Deploy container:**
   ```bash
   az container create \
     --resource-group rag-agent-rg \
     --name rag-agent \
     --image your-registry/dynamic-rag-agent:latest \
     --dns-name-label rag-agent-unique \
     --ports 8000 \
     --environment-variables ENVIRONMENT=production \
     --secure-environment-variables SUPABASE_URL=your-url SUPABASE_KEY=your-key
   ```

## Monitoring and Maintenance

### Health Monitoring

1. **Setup health check monitoring:**
   ```bash
   # Simple health check script
   #!/bin/bash
   response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
   if [ $response != "200" ]; then
     echo "Health check failed: $response"
     # Send alert
   fi
   ```

2. **Add to crontab:**
   ```bash
   # Check every 5 minutes
   */5 * * * * /path/to/health-check.sh
   ```

### Log Management

1. **Setup log rotation:**
   ```bash
   # /etc/logrotate.d/rag-agent
   /opt/rag-agent/logs/*.log {
       daily
       missingok
       rotate 30
       compress
       delaycompress
       notifempty
       create 644 raguser raguser
       postrotate
           systemctl reload rag-agent
       endscript
   }
   ```

### Backup Strategy

1. **Database backups** (handled by Supabase)
2. **Configuration backups:**
   ```bash
   # Backup script
   #!/bin/bash
   tar -czf /backup/rag-agent-config-$(date +%Y%m%d).tar.gz \
     /opt/rag-agent/.env \
     /opt/rag-agent/gunicorn.conf.py \
     /etc/nginx/sites-available/rag-agent
   ```

### Updates and Maintenance

1. **Zero-downtime deployment:**
   ```bash
   # Using Docker Compose
   docker-compose pull
   docker-compose up -d --no-deps backend
   ```

2. **Database migrations:**
   ```bash
   # Run any necessary Supabase migrations
   # Update environment variables if needed
   ```

### Performance Monitoring

1. **Use APM tools:**
   - New Relic
   - DataDog
   - Prometheus + Grafana

2. **Monitor key metrics:**
   - Response times
   - Error rates
   - Memory usage
   - Database performance
   - Queue lengths

### Security Updates

1. **Regular updates:**
   ```bash
   # Update dependencies
   pip install -r requirements.txt --upgrade
   
   # Update base image
   docker pull python:3.11-slim
   docker-compose build --no-cache
   ```

2. **Security scanning:**
   ```bash
   # Scan for vulnerabilities
   pip-audit
   docker scout cves
   ```

This deployment guide covers the most common scenarios. Choose the deployment method that best fits your infrastructure and requirements.
