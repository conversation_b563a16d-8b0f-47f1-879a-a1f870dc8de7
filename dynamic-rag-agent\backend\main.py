import os
import uuid
import time
from typing import Optional
from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
import requests
from agent import run_agent
from config import get_settings
from logging_config import setup_logging
from exceptions import AgentError, TTSError
from health import router as health_router
import logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create directory if missing
os.makedirs(settings.audio_dir, exist_ok=True)

# FastAPI app setup
app = FastAPI(
    title="Dynamic RAG Agent API",
    description="AI Agent with RAG capabilities and TTS support",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    logger.info(f"{request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
    return response

# Add security headers middleware
@app.middleware("http")
async def add_security_headers(request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    return response

# Include health check router
app.include_router(health_router, tags=["health"])

# Request/Response models
class Query(BaseModel):
    message: str

class AgentResponse(BaseModel):
    response: str
    audio_url: Optional[str] = None

# Utility functions
def cleanup_old_audio():
    """Clean up old audio files to prevent disk space issues"""
    try:
        for filename in os.listdir(settings.audio_dir):
            if filename.endswith(".wav"):
                file_path = os.path.join(settings.audio_dir, filename)
                try:
                    os.remove(file_path)
                    logger.info(f"Cleaned up audio file: {filename}")
                except Exception as e:
                    logger.warning(f"Failed to remove {filename}: {e}")
    except Exception as e:
        logger.error(f"Error during audio cleanup: {e}")

# API Endpoints
@app.post("/chat", response_model=AgentResponse)
async def chat_endpoint(query: Query):
    """Basic chat endpoint without TTS"""
    try:
        response_text = run_agent(query.message)
        return AgentResponse(response=response_text)
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise AgentError(f"Chat processing failed: {str(e)}")

@app.post("/agent-audio", response_model=AgentResponse)
async def run_agent_with_audio(query: Query, background_tasks: BackgroundTasks):
    """Chat endpoint with TTS audio generation"""
    try:
        # Run agent
        response_text = run_agent(query.message)

        # Generate audio from Chatterbox if available
        audio_url = None
        try:
            response = requests.post(
                f"{settings.chatterbox_url}/api/tts",
                json={"input": response_text, "voice": settings.tts_voice},
                timeout=10
            )
            response.raise_for_status()

            # Save audio file
            filename = f"{uuid.uuid4()}.wav"
            audio_path = os.path.join(settings.audio_dir, filename)
            with open(audio_path, "wb") as f:
                f.write(response.content)

            audio_url = f"/audio/{filename}"
            logger.info(f"Generated audio file: {filename}")

            # Schedule cleanup
            background_tasks.add_task(cleanup_old_audio)

        except Exception as e:
            logger.warning(f"TTS generation failed: {e}")
            # Continue without audio - TTS is optional

        return AgentResponse(response=response_text, audio_url=audio_url)

    except Exception as e:
        logger.error(f"Error in agent-audio endpoint: {e}")
        raise AgentError(f"Agent processing failed: {str(e)}")

@app.get("/audio/{filename}")
def serve_audio(filename: str):
    """Serve audio files"""
    try:
        file_path = os.path.join(settings.audio_dir, filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Audio file not found")
        return FileResponse(file_path, media_type="audio/wav")
    except Exception as e:
        logger.error(f"Error serving audio file {filename}: {e}")
        raise HTTPException(status_code=500, detail="Error serving audio file")

@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "dynamic-rag-agent"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        log_level=settings.log_level.lower(),
        reload=settings.debug
    )
