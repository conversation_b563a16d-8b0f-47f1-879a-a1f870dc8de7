"""
Custom exceptions and error handlers for the Dynamic RAG Agent
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging

logger = logging.getLogger(__name__)


class AgentError(Exception):
    """Base exception for agent-related errors"""
    def __init__(self, message: str, error_code: str = "AGENT_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)


class RAGError(AgentError):
    """Exception for RAG-related errors"""
    def __init__(self, message: str):
        super().__init__(message, "RAG_ERROR")


class EmbeddingError(AgentError):
    """Exception for embedding-related errors"""
    def __init__(self, message: str):
        super().__init__(message, "EMBEDDING_ERROR")


class TTSError(AgentError):
    """Exception for TTS-related errors"""
    def __init__(self, message: str):
        super().__init__(message, "TTS_ERROR")


async def agent_error_handler(request: Request, exc: AgentError):
    """Handle custom agent errors"""
    logger.error(f"Agent error: {exc.error_code} - {exc.message}")
    return JSONResponse(
        status_code=500,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "request_id": getattr(request.state, "request_id", None)
        }
    )


async def validation_error_handler(request: Request, exc: RequestValidationError):
    """Handle validation errors"""
    logger.warning(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "error": "VALIDATION_ERROR",
            "message": "Invalid request data",
            "details": exc.errors(),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


async def http_error_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP errors"""
    logger.warning(f"HTTP error {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": f"HTTP_{exc.status_code}",
            "message": exc.detail,
            "request_id": getattr(request.state, "request_id", None)
        }
    )


async def general_error_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unhandled error: {type(exc).__name__}: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "INTERNAL_ERROR",
            "message": "An internal error occurred",
            "request_id": getattr(request.state, "request_id", None)
        }
    )
