"""
Tests for the configuration module
"""
import pytest
import os
from unittest.mock import patch
from config import Settings, get_settings


def test_settings_with_env_vars(monkeypatch):
    """Test settings loading with environment variables"""
    # Set environment variables
    monkeypatch.setenv("SUPABASE_URL", "http://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-key")
    monkeypatch.setenv("GOOGLE_API_KEY", "test-google-key")
    monkeypatch.setenv("OLLAMA_URL", "http://test-ollama:11434")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")
    monkeypatch.setenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:5173")
    
    settings = Settings()
    
    assert settings.supabase_url == "http://test.supabase.co"
    assert settings.supabase_key == "test-key"
    assert settings.google_api_key == "test-google-key"
    assert settings.ollama_url == "http://test-ollama:11434"
    assert settings.log_level == "DEBUG"
    assert settings.cors_origins == ["http://localhost:3000", "http://localhost:5173"]


def test_settings_defaults():
    """Test default settings values"""
    with patch.dict(os.environ, {
        "SUPABASE_URL": "http://test.supabase.co",
        "SUPABASE_KEY": "test-key",
        "GOOGLE_API_KEY": "test-google-key"
    }, clear=True):
        settings = Settings()
        
        # Test defaults
        assert settings.ollama_url == "http://localhost:11434"
        assert settings.embedding_model == "nomic-embed-text"
        assert settings.chatterbox_url == "http://localhost:8004"
        assert settings.tts_voice == "default"
        assert settings.log_level == "INFO"
        assert settings.host == "0.0.0.0"
        assert settings.port == 8000
        assert settings.environment == "development"
        assert settings.debug is True


def test_cors_origins_parsing(monkeypatch):
    """Test CORS origins parsing from comma-separated string"""
    monkeypatch.setenv("SUPABASE_URL", "http://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-key")
    monkeypatch.setenv("GOOGLE_API_KEY", "test-google-key")
    monkeypatch.setenv("CORS_ORIGINS", "http://localhost:3000, http://localhost:5173, http://example.com")
    
    settings = Settings()
    
    expected_origins = ["http://localhost:3000", "http://localhost:5173", "http://example.com"]
    assert settings.cors_origins == expected_origins


def test_get_settings():
    """Test get_settings function"""
    with patch.dict(os.environ, {
        "SUPABASE_URL": "http://test.supabase.co",
        "SUPABASE_KEY": "test-key",
        "GOOGLE_API_KEY": "test-google-key"
    }):
        settings = get_settings()
        assert isinstance(settings, Settings)
        assert settings.supabase_url == "http://test.supabase.co"


def test_settings_validation_missing_required():
    """Test settings validation with missing required fields"""
    with patch.dict(os.environ, {}, clear=True):
        with pytest.raises(Exception):  # Should raise validation error
            Settings()


def test_production_settings(monkeypatch):
    """Test production-specific settings"""
    monkeypatch.setenv("SUPABASE_URL", "http://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-key")
    monkeypatch.setenv("GOOGLE_API_KEY", "test-google-key")
    monkeypatch.setenv("ENVIRONMENT", "production")
    monkeypatch.setenv("DEBUG", "false")
    monkeypatch.setenv("LOG_LEVEL", "WARNING")
    
    settings = Settings()
    
    assert settings.environment == "production"
    assert settings.debug is False
    assert settings.log_level == "WARNING"


def test_audio_dir_setting(monkeypatch):
    """Test audio directory setting"""
    monkeypatch.setenv("SUPABASE_URL", "http://test.supabase.co")
    monkeypatch.setenv("SUPABASE_KEY", "test-key")
    monkeypatch.setenv("GOOGLE_API_KEY", "test-google-key")
    monkeypatch.setenv("AUDIO_DIR", "/custom/audio/path")
    
    settings = Settings()
    
    assert settings.audio_dir == "/custom/audio/path"
