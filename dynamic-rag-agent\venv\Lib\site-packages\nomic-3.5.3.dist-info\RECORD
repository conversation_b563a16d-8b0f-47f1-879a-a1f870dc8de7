../../Scripts/nomic.exe,sha256=SsjRs7-oPK6mU0E5bMSAKXXjVWclvB5l3Q8s7jpA0rk,108425
nomic-3.5.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
nomic-3.5.3.dist-info/METADATA,sha256=SZ-1O-bYQnQ75IBcWf5Qn_8lF69kEqOIM71uVL8lcSY,9104
nomic-3.5.3.dist-info/RECORD,,
nomic-3.5.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nomic-3.5.3.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
nomic-3.5.3.dist-info/entry_points.txt,sha256=fSsdAqbhipoheW7fMHfW9Xwb0z2SEYqM23EpcJXPXvA,40
nomic-3.5.3.dist-info/top_level.txt,sha256=HWnur9g16r0GM4AanKT9SEaYuq6COTbC6GeiqzuTORU,6
nomic/__init__.py,sha256=hLPpd1dIXjPcYL9PMNl0vD4shWcwKsNKhHSfq9ZA06k,68
nomic/__pycache__/__init__.cpython-313.pyc,,
nomic/__pycache__/atlas.cpython-313.pyc,,
nomic/__pycache__/cli.cpython-313.pyc,,
nomic/__pycache__/data_inference.cpython-313.pyc,,
nomic/__pycache__/data_operations.cpython-313.pyc,,
nomic/__pycache__/dataset.cpython-313.pyc,,
nomic/__pycache__/embed.cpython-313.pyc,,
nomic/__pycache__/settings.cpython-313.pyc,,
nomic/__pycache__/utils.cpython-313.pyc,,
nomic/atlas.py,sha256=t3ZKt24fvqN3pQ20SUz07Q-gtx8-tp9UfTT4u292AIA,11592
nomic/aws/__init__.py,sha256=UQWG0GOFDtID0j7Gc3Dwn3ehEZZJ2rpBONVyhobcm54,424
nomic/aws/__pycache__/__init__.cpython-313.pyc,,
nomic/aws/__pycache__/sagemaker.cpython-313.pyc,,
nomic/aws/sagemaker.py,sha256=OXIgZo7QLPptfvVPr2dkcrw1nDP31AhFtsjN_H1ukjk,10328
nomic/cli.py,sha256=7J0L0j1WheL-pNFqOO2997yD9dsgvLEEOuB33tRjfvY,6070
nomic/data_inference.py,sha256=IFIw1AIQJvs8Gko_x6tAA0AOmmXaJdNN3Ueg_8ejszE,5104
nomic/data_operations.py,sha256=BMoZzisfXbC5IjfFIqZb_LHzVb6xRUBZPYCTNJZVzm4,49670
nomic/dataset.py,sha256=8dyZjTIMqT7ipjKHs7E0evH8QM0ESEhG-oISAGIzG30,68543
nomic/embed.py,sha256=5EWCkAgxziHn0RrbWlK9bwNqTdZJ2D-_BuQ9APPoqXE,14138
nomic/pl_callbacks/__init__.py,sha256=dVHXtPr2LD6j50d727QWQcl5ATurcBFpf7wcKNkdirk,48
nomic/pl_callbacks/__pycache__/__init__.cpython-313.pyc,,
nomic/pl_callbacks/__pycache__/pl_callback.cpython-313.pyc,,
nomic/pl_callbacks/pl_callback.py,sha256=Gben9ecWWpz0riWZvtdsdMv22NbKqs3WfPi1cZvxnRk,7597
nomic/settings.py,sha256=ilhWhrTOPscQ-t6glIrxW9BKCq3g5OvwhSoIWTcavcg,612
nomic/utils.py,sha256=HdRF1Xx9-01p9qfnq8mXCwHmOsZX6IpXswUXuuYWYX4,6495
