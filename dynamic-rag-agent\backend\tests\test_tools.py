"""
Tests for the tools module
"""
import pytest
from unittest.mock import Mock, patch
from tools.supabase_rag_tool import SupabaseRAGTool


@patch('tools.supabase_rag_tool.supabase')
@patch('tools.supabase_rag_tool.get_embedding')
def test_supabase_rag_tool_success(mock_get_embedding, mock_supabase):
    """Test successful RAG tool execution"""
    # Setup mocks
    mock_get_embedding.return_value = [0.1] * 768
    mock_result = Mock()
    mock_result.data = [
        {"content": "Document 1 content"},
        {"content": "Document 2 content"}
    ]
    mock_supabase.rpc.return_value.execute.return_value = mock_result
    
    # Test
    tool = SupabaseRAGTool()
    result = tool.run("test query")
    
    # Assertions
    assert "Document 1 content" in result
    assert "Document 2 content" in result
    assert "---" in result  # Separator
    mock_get_embedding.assert_called_once_with("test query")
    mock_supabase.rpc.assert_called_once_with("match_documents", {
        "query_embedding": [0.1] * 768,
        "match_threshold": 0.75,
        "match_count": 5
    })


@patch('tools.supabase_rag_tool.supabase')
@patch('tools.supabase_rag_tool.get_embedding')
def test_supabase_rag_tool_no_results(mock_get_embedding, mock_supabase):
    """Test RAG tool when no documents are found"""
    # Setup mocks
    mock_get_embedding.return_value = [0.1] * 768
    mock_result = Mock()
    mock_result.data = []
    mock_supabase.rpc.return_value.execute.return_value = mock_result
    
    # Test
    tool = SupabaseRAGTool()
    result = tool.run("test query")
    
    # Assertions
    assert result == "No relevant information found."


@patch('tools.supabase_rag_tool.supabase')
@patch('tools.supabase_rag_tool.get_embedding')
def test_supabase_rag_tool_embedding_error(mock_get_embedding, mock_supabase):
    """Test RAG tool when embedding generation fails"""
    # Setup mocks
    mock_get_embedding.side_effect = Exception("Embedding failed")
    
    # Test
    tool = SupabaseRAGTool()
    result = tool.run("test query")
    
    # Assertions
    assert "Search failed" in result
    assert "Embedding failed" in result


@patch('tools.supabase_rag_tool.supabase')
@patch('tools.supabase_rag_tool.get_embedding')
def test_supabase_rag_tool_database_error(mock_get_embedding, mock_supabase):
    """Test RAG tool when database query fails"""
    # Setup mocks
    mock_get_embedding.return_value = [0.1] * 768
    mock_supabase.rpc.return_value.execute.side_effect = Exception("Database error")
    
    # Test
    tool = SupabaseRAGTool()
    result = tool.run("test query")
    
    # Assertions
    assert "Search failed" in result
    assert "Database error" in result


def test_supabase_rag_tool_attributes():
    """Test RAG tool has required attributes"""
    tool = SupabaseRAGTool()
    
    assert hasattr(tool, 'name')
    assert hasattr(tool, 'description')
    assert hasattr(tool, 'run')
    assert tool.name == "supabase_rag"
    assert "search" in tool.description.lower()


@patch('tools.supabase_rag_tool.logger')
@patch('tools.supabase_rag_tool.supabase')
@patch('tools.supabase_rag_tool.get_embedding')
def test_supabase_rag_tool_logging(mock_get_embedding, mock_supabase, mock_logger):
    """Test that RAG tool operations are logged"""
    # Setup mocks
    mock_get_embedding.return_value = [0.1] * 768
    mock_result = Mock()
    mock_result.data = [{"content": "Test content"}]
    mock_supabase.rpc.return_value.execute.return_value = mock_result
    
    # Test
    tool = SupabaseRAGTool()
    tool.run("test query")
    
    # Check logging calls
    mock_logger.info.assert_called()
    log_calls = [call.args[0] for call in mock_logger.info.call_args_list]
    assert any("Searching for:" in call for call in log_calls)
    assert any("Found" in call and "documents" in call for call in log_calls)
