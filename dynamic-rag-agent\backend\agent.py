from google.adk.agents import Agent
from google.adk.runners import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from google.adk.tools import (
    google_search,
    calculator,
    code_execution,
    units,
    wikipedia
)
from backend.tools.supabase_rag_tool import SupabaseRAGTool
from google.genai.types import Content, Part

# Define tools
tools = [
    SupabaseRAGTool(),                     # your RAG tool
    google_search.GoogleSearchTool(),     # web search
    calculator.CalculatorTool(),          # math support
    code_execution.CodeExecutionTool(),   # python exec
    units.UnitConversionTool(),           # metric/imperial
    wikipedia.WikipediaTool(),            # Wikipedia fallback
    # Add more tools here in future
]

# Initialize agent
agent = Agent(tools=tools)

# Create runner
runner = InMemoryRunner(agent)

# Run agent
def run_agent(message: str) -> str:
    content = Content(parts=[Part(text=message)], role="user")
    result = runner.run(content)
    return result.text if hasattr(result, "text") else str(result)
