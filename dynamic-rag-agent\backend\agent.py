from google.adk.agents import Agent
from google.adk.runners import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from google.adk.tools import (
    google_search,
    calculator,
    code_execution,
    units,
    wikipedia,
)
from tools.supabase_rag_tool import SupabaseRAGTool
from agent.tools.supabase_search_tool import SupabaseSearchTool  # if you have this separately
from google.genai.types import Content, Part
import requests

from fastapi.responses import FileResponse
import httpx
import uuid

@app.post("/agent")
async def run_agent(query: Query):
    content = Content(parts=[Part(text=query.message)], role="user")
    result = runner.run(content)
    text = result.text if hasattr(result, "text") else str(result)

    # Now generate speech
    output_filename = f"{uuid.uuid4()}.wav"
    output_path = os.path.join(CHATTERBOX_OUTPUT_DIR, output_filename)

    async with httpx.AsyncClient() as client:
        tts_response = await client.post(f"{CHATTERBOX_URL}/speak", json={
            "text": text,
            "voice": "default",
            "output_path": output_path
        })

    audio_url = None
    if tts_response.status_code == 200:
        audio_url = f"http://localhost:8000/audio/{output_filename}"

    return {
        "response": text,
        "audio_url": audio_url
    }


# Embedding configuration
OLLAMA_URL = "http://localhost:11434"
EMBEDDING_MODEL = "nomic-embed-text"

def get_embedding(prompt: str) -> list[float]:
    response = requests.post(
        f"{OLLAMA_URL}/api/embeddings",
        json={"model": EMBEDDING_MODEL, "prompt": prompt}
    )
    response.raise_for_status()
    return response.json()["embedding"]

# Define tools — only once!
tools = [
    SupabaseRAGTool(),                     # custom RAG search
    SupabaseSearchTool(),                 # optional: if it's a separate search tool
    google_search.GoogleSearchTool(),     # live search
    calculator.CalculatorTool(),          # math ops
    code_execution.CodeExecutionTool(),   # run Python
    units.UnitConversionTool(),           # metric/imperial
    wikipedia.WikipediaTool(),            # fallback lookup
]

# Create and run the agent
agent = Agent(tools=tools)
runner = InMemoryRunner(agent)

def run_agent(message: str) -> str:
    content = Content(parts=[Part(text=message)], role="user")
    result = runner.run(content)
    return result.text if hasattr(result, "text") else str(result)
