# Core FastAPI dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Google AI and ADK
google-generativeai
google-adk
google-api-python-client
google-auth
google-auth-oauthlib
google-auth-httplib2

# Database and storage
supabase-py>=2.0.0

# HTTP client
httpx>=0.25.0
requests>=2.31.0

# Environment and configuration
python-dotenv>=1.0.0

# Utilities
tenacity>=8.2.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0
coverage>=7.3.0

# Code quality
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0

# System monitoring
psutil>=5.9.0
