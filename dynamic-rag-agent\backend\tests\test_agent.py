"""
Tests for the agent module
"""
import pytest
from unittest.mock import Mock, patch
from agent import run_agent


@patch('agent.runner')
@patch('agent.Content')
@patch('agent.Part')
def test_run_agent_success(mock_part, mock_content, mock_runner):
    """Test successful agent execution"""
    # Setup mocks
    mock_result = Mock()
    mock_result.text = "Test response"
    mock_runner.run.return_value = mock_result
    
    # Test
    response = run_agent("test message")
    
    # Assertions
    assert response == "Test response"
    mock_part.assert_called_once_with(text="test message")
    mock_content.assert_called_once()
    mock_runner.run.assert_called_once()


@patch('agent.runner')
@patch('agent.Content')
@patch('agent.Part')
def test_run_agent_no_text_attribute(mock_part, mock_content, mock_runner):
    """Test agent execution when result has no text attribute"""
    # Setup mocks
    mock_result = Mock(spec=[])  # No text attribute
    mock_result.__str__ = Mock(return_value="String representation")
    mock_runner.run.return_value = mock_result
    
    # Test
    response = run_agent("test message")
    
    # Assertions
    assert response == "String representation"


@patch('agent.runner')
def test_run_agent_exception(mock_runner):
    """Test agent execution with exception"""
    # Setup mock to raise exception
    mock_runner.run.side_effect = Exception("Agent failed")
    
    # Test
    with pytest.raises(Exception) as exc_info:
        run_agent("test message")
    
    # Assertions
    assert "Agent execution failed" in str(exc_info.value)


@patch('agent.SupabaseRAGTool')
def test_agent_initialization(mock_rag_tool):
    """Test agent initialization"""
    # Import after patching to ensure mock is used
    from agent import agent, runner, tools
    
    # Assertions
    assert agent is not None
    assert runner is not None
    assert len(tools) >= 1  # At least the RAG tool
    mock_rag_tool.assert_called()


def test_tools_loading():
    """Test that tools are loaded correctly"""
    from agent import tools
    
    # Should have at least the Supabase RAG tool
    assert len(tools) >= 1
    
    # Check that tools have required attributes
    for tool in tools:
        assert hasattr(tool, 'name') or hasattr(tool, '__class__')


@patch('agent.logger')
def test_agent_logging(mock_logger):
    """Test that agent operations are logged"""
    with patch('agent.runner') as mock_runner:
        mock_result = Mock()
        mock_result.text = "Test response"
        mock_runner.run.return_value = mock_result
        
        run_agent("test message")
        
        # Check that success was logged
        mock_logger.info.assert_called_with("Agent processed message successfully")
