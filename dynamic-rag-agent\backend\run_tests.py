#!/usr/bin/env python3
"""
Test runner script for the Dynamic RAG Agent
"""
import sys
import subprocess
import argparse
from pathlib import Path


def run_tests(test_type="all", verbose=False, coverage=True, parallel=False):
    """Run tests with specified options"""
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add verbosity
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Add coverage
    if coverage:
        cmd.extend([
            "--cov=.",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov"
        ])
    
    # Add parallel execution
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # Select test type
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "fast":
        cmd.extend(["-m", "not slow"])
    elif test_type == "slow":
        cmd.extend(["-m", "slow"])
    elif test_type != "all":
        # Specific test file or pattern
        cmd.append(test_type)
    
    # Add test directory
    cmd.append("tests/")
    
    print(f"Running command: {' '.join(cmd)}")
    
    # Run tests
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return 1
    except Exception as e:
        print(f"Error running tests: {e}")
        return 1


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Run tests for Dynamic RAG Agent")
    parser.add_argument(
        "test_type",
        nargs="?",
        default="all",
        help="Type of tests to run: all, unit, integration, fast, slow, or specific test file"
    )
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Disable coverage reporting"
    )
    parser.add_argument(
        "-p", "--parallel",
        action="store_true",
        help="Run tests in parallel"
    )
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install test dependencies first"
    )
    
    args = parser.parse_args()
    
    # Install dependencies if requested
    if args.install_deps:
        print("Installing test dependencies...")
        subprocess.run([
            "pip", "install", "-r", "requirements.txt",
            "pytest", "pytest-cov", "pytest-asyncio", "pytest-mock"
        ])
    
    # Check if we're in the right directory
    if not Path("tests").exists():
        print("Error: tests directory not found. Run this script from the backend directory.")
        return 1
    
    # Run tests
    return run_tests(
        test_type=args.test_type,
        verbose=args.verbose,
        coverage=not args.no_coverage,
        parallel=args.parallel
    )


if __name__ == "__main__":
    sys.exit(main())
