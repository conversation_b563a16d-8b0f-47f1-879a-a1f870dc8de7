"""
Tests for the utils module
"""
import pytest
from unittest.mock import Mock, patch
from utils.embedding import get_embedding


@patch('utils.embedding.requests.post')
def test_get_embedding_success(mock_post):
    """Test successful embedding generation"""
    # Setup mock
    mock_response = Mock()
    mock_response.json.return_value = {"embedding": [0.1, 0.2, 0.3]}
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response
    
    # Test
    result = get_embedding("test text")
    
    # Assertions
    assert result == [0.1, 0.2, 0.3]
    mock_post.assert_called_once()
    call_args = mock_post.call_args
    assert "api/embeddings" in call_args[0][0]
    assert call_args[1]["json"]["prompt"] == "test text"


@patch('utils.embedding.requests.post')
def test_get_embedding_http_error(mock_post):
    """Test embedding generation with HTTP error"""
    # Setup mock to raise HTTP error
    mock_response = Mock()
    mock_response.raise_for_status.side_effect = Exception("HTTP 500 Error")
    mock_post.return_value = mock_response
    
    # Test
    with pytest.raises(Exception) as exc_info:
        get_embedding("test text")
    
    # Assertions
    assert "Embedding generation failed" in str(exc_info.value)


@patch('utils.embedding.requests.post')
def test_get_embedding_connection_error(mock_post):
    """Test embedding generation with connection error"""
    # Setup mock to raise connection error
    mock_post.side_effect = Exception("Connection refused")
    
    # Test
    with pytest.raises(Exception) as exc_info:
        get_embedding("test text")
    
    # Assertions
    assert "Embedding generation failed" in str(exc_info.value)


@patch('utils.embedding.requests.post')
def test_get_embedding_timeout(mock_post):
    """Test embedding generation with timeout"""
    # Setup mock to raise timeout
    mock_post.side_effect = Exception("Timeout")
    
    # Test
    with pytest.raises(Exception) as exc_info:
        get_embedding("test text")
    
    # Assertions
    assert "Embedding generation failed" in str(exc_info.value)


@patch('utils.embedding.requests.post')
@patch('utils.embedding.settings')
def test_get_embedding_uses_settings(mock_settings, mock_post):
    """Test that embedding function uses configuration settings"""
    # Setup mocks
    mock_settings.ollama_url = "http://custom-ollama:11434"
    mock_settings.embedding_model = "custom-model"
    
    mock_response = Mock()
    mock_response.json.return_value = {"embedding": [0.1, 0.2, 0.3]}
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response
    
    # Test
    get_embedding("test text")
    
    # Assertions
    call_args = mock_post.call_args
    assert "http://custom-ollama:11434/api/embeddings" in call_args[0][0]
    assert call_args[1]["json"]["model"] == "custom-model"


@patch('utils.embedding.requests.post')
def test_get_embedding_empty_text(mock_post):
    """Test embedding generation with empty text"""
    # Setup mock
    mock_response = Mock()
    mock_response.json.return_value = {"embedding": [0.0] * 768}
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response
    
    # Test
    result = get_embedding("")
    
    # Assertions
    assert len(result) == 768
    assert all(x == 0.0 for x in result)


@patch('utils.embedding.logger')
@patch('utils.embedding.requests.post')
def test_get_embedding_logging(mock_post, mock_logger):
    """Test that embedding operations are logged"""
    # Setup mock
    mock_response = Mock()
    mock_response.json.return_value = {"embedding": [0.1] * 768}
    mock_response.raise_for_status.return_value = None
    mock_post.return_value = mock_response
    
    # Test
    get_embedding("test text")
    
    # Check that debug log was called
    mock_logger.debug.assert_called()
    log_message = mock_logger.debug.call_args[0][0]
    assert "Generated embedding of length" in log_message
