import requests
import logging
from config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

def get_embedding(text: str) -> list[float]:
    """
    Get embedding for text using Ollama

    Args:
        text: Text to embed

    Returns:
        List of floats representing the embedding

    Raises:
        Exception: If embedding generation fails
    """
    try:
        response = requests.post(
            f"{settings.ollama_url}/api/embeddings",
            json={
                "model": settings.embedding_model,
                "prompt": text
            },
            timeout=30
        )
        response.raise_for_status()
        embedding = response.json()["embedding"]
        logger.debug(f"Generated embedding of length {len(embedding)}")
        return embedding
    except Exception as e:
        logger.error(f"Failed to generate embedding: {e}")
        raise Exception(f"Embedding generation failed: {str(e)}")
