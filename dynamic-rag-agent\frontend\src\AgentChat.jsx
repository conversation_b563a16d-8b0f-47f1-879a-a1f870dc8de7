import { useState } from "react";

export default function AgentChat() {
  const [prompt, setPrompt] = useState("");
  const [response, setResponse] = useState("");

  const handleAsk = async () => {
  const response = await fetch('http://localhost:8000/agent', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ message: userInput }),
  });

  const { response: text, audio_url } = await response.json();
  setOutput(text);

  if (audio_url) {
    const audio = new Audio(audio_url);
    audio.play();
  }
};


  const handleSubmit = async (e) => {
    e.preventDefault();
    const res = await fetch("http://localhost:8000/ask", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ prompt }),
    });

    const data = await res.json();
    setResponse(data.response);
  };

  return (
    <div>
      <form onSubmit={handleSubmit} className="flex gap-2 p-4">
        <input
          className="flex-1 border p-2 rounded"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Ask me anything..."
        />
        <button className="bg-blue-500 text-white px-4 py-2 rounded">
          Send
        </button>
      </form>
      <div className="p-4">
        <strong>Response:</strong> {response}
      </div>
    </div>
  );
}
