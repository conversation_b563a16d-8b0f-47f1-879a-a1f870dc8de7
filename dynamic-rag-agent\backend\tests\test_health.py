"""
Tests for the health check module
"""
import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient


def test_basic_health_check(client):
    """Test basic health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert "status" in data
    assert "timestamp" in data
    assert "uptime" in data
    assert "version" in data
    assert "system" in data
    assert "services" in data
    
    assert data["status"] in ["healthy", "degraded", "unhealthy"]
    assert data["version"] == "1.0.0"
    assert isinstance(data["uptime"], (int, float))
    assert data["uptime"] >= 0


def test_detailed_health_check(client):
    """Test detailed health check endpoint"""
    response = client.get("/health/detailed")
    assert response.status_code == 200
    
    data = response.json()
    assert "status" in data
    assert "dependencies" in data
    
    # Check dependencies structure
    dependencies = data["dependencies"]
    assert "supabase" in dependencies
    assert "ollama" in dependencies
    assert "chatterbox" in dependencies
    
    for service, info in dependencies.items():
        assert "status" in info
        assert info["status"] in ["healthy", "degraded", "unhealthy"]


def test_metrics_endpoint(client):
    """Test metrics endpoint"""
    response = client.get("/metrics")
    assert response.status_code == 200
    
    data = response.json()
    assert "uptime_seconds" in data
    assert "timestamp" in data
    assert "system" in data
    assert isinstance(data["uptime_seconds"], (int, float))


@patch('health.check_supabase_health')
def test_health_check_supabase_healthy(mock_check_supabase, client):
    """Test health check when Supabase is healthy"""
    mock_check_supabase.return_value = {
        "status": "healthy",
        "response_time": 0.1,
        "details": "Connection successful"
    }
    
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["services"]["supabase"] == "healthy"


@patch('health.check_supabase_health')
def test_health_check_supabase_unhealthy(mock_check_supabase, client):
    """Test health check when Supabase is unhealthy"""
    mock_check_supabase.return_value = {
        "status": "unhealthy",
        "error": "Connection failed",
        "details": "Connection failed"
    }
    
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["services"]["supabase"] == "unhealthy"
    assert data["status"] == "unhealthy"  # Overall status should be unhealthy


@patch('health.check_ollama_health')
def test_health_check_ollama_healthy(mock_check_ollama, client):
    """Test health check when Ollama is healthy"""
    mock_check_ollama.return_value = {
        "status": "healthy",
        "response_time": 0.2,
        "details": "Service available"
    }
    
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["services"]["ollama"] == "healthy"


@patch('health.requests.get')
def test_check_ollama_health_success(mock_get):
    """Test Ollama health check success"""
    from health import check_ollama_health
    from config import get_settings
    
    # Setup mock
    mock_response = Mock()
    mock_response.raise_for_status.return_value = None
    mock_response.elapsed.total_seconds.return_value = 0.1
    mock_get.return_value = mock_response
    
    settings = get_settings()
    result = check_ollama_health(settings)
    
    assert result["status"] == "healthy"
    assert result["response_time"] == 0.1


@patch('health.requests.get')
def test_check_ollama_health_failure(mock_get):
    """Test Ollama health check failure"""
    from health import check_ollama_health
    from config import get_settings
    
    # Setup mock to raise exception
    mock_get.side_effect = Exception("Connection refused")
    
    settings = get_settings()
    result = check_ollama_health(settings)
    
    assert result["status"] == "unhealthy"
    assert "Connection refused" in result["error"]


@patch('health.psutil.cpu_percent')
@patch('health.psutil.virtual_memory')
@patch('health.psutil.disk_usage')
def test_get_system_info(mock_disk, mock_memory, mock_cpu):
    """Test system information gathering"""
    from health import get_system_info
    
    # Setup mocks
    mock_cpu.return_value = 25.5
    mock_memory.return_value.percent = 60.0
    mock_disk.return_value.percent = 45.0
    
    result = get_system_info()
    
    assert result["cpu_percent"] == 25.5
    assert result["memory_percent"] == 60.0
    assert result["disk_percent"] == 45.0
